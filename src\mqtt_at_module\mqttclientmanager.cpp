#include "mqttclientmanager.h"
#include <QDebug>

Q_LOGGING_CATEGORY(mqttClientManagerLog, "mqtt.clientmanager")

MqttClientManager::MqttClientManager(QObject *parent)
    : QObject(parent)
{
    qCDebug(mqttClientManagerLog) << "MqttClientManager created";
    initializeClients();
}

MqttClientManager::~MqttClientManager()
{
    qCDebug(mqttClientManagerLog) << "MqttClientManager destroyed";
    
    // 清理所有客户端实例
    for (auto it = m_clients.begin(); it != m_clients.end(); ++it) {
        delete it.value();
    }
    m_clients.clear();
}

// === AT指令处理接口实现 ===

bool MqttClientManager::handleQmtOpen(int clientIdx, const QString& hostname, int port)
{
    qCDebug(mqttClientManagerLog) << "handleQmtOpen:" << clientIdx << hostname << port;
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        qCWarning(mqttClientManagerLog) << "Invalid client index:" << clientIdx;
        return false;
    }
    
    return client->openNetwork(hostname, port);
}

bool MqttClientManager::handleQmtConn(int clientIdx, const QString& clientId, 
                                      const QString& username, const QString& password)
{
    qCDebug(mqttClientManagerLog) << "handleQmtConn:" << clientIdx << clientId;
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        qCWarning(mqttClientManagerLog) << "Invalid client index:" << clientIdx;
        return false;
    }
    
    return client->connectMqtt(clientId, username, password);
}

bool MqttClientManager::handleQmtSub(int clientIdx, int msgId, const QString& topic, int qos)
{
    qCDebug(mqttClientManagerLog) << "handleQmtSub:" << clientIdx << msgId << topic << qos;
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        qCWarning(mqttClientManagerLog) << "Invalid client index:" << clientIdx;
        return false;
    }
    
    return client->subscribe(msgId, topic, qos);
}

bool MqttClientManager::handleQmtPubex(int clientIdx, int msgId, int qos, bool retain,
                                       const QString& topic, const QByteArray& payload)
{
    qCDebug(mqttClientManagerLog) << "handleQmtPubex:" << clientIdx << msgId << topic;
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        qCWarning(mqttClientManagerLog) << "Invalid client index:" << clientIdx;
        return false;
    }
    
    return client->publish(msgId, qos, retain, topic, payload);
}

bool MqttClientManager::handleQmtDisc(int clientIdx)
{
    qCDebug(mqttClientManagerLog) << "handleQmtDisc:" << clientIdx;
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        qCWarning(mqttClientManagerLog) << "Invalid client index:" << clientIdx;
        return false;
    }
    
    return client->disconnect();
}

bool MqttClientManager::handleQmtClose(int clientIdx)
{
    qCDebug(mqttClientManagerLog) << "handleQmtClose:" << clientIdx;
    
    MqttClient* client = getClient(clientIdx);
    if (!client) {
        qCWarning(mqttClientManagerLog) << "Invalid client index:" << clientIdx;
        return false;
    }
    
    return client->closeNetwork();
}

// === 状态查询接口实现 ===

QString MqttClientManager::handleQmtOpenQuery() const
{
    qCDebug(mqttClientManagerLog) << "handleQmtOpenQuery";
    
    QStringList results;
    
    for (int i = 0; i < MAX_CLIENTS; ++i) {
        MqttClient* client = getClient(i);
        if (client && client->isNetworkOpen()) {
            QString info = client->getNetworkInfo();
            if (!info.isEmpty()) {
                results << info;
            }
        }
    }
    
    return results.join("\r\n");
}

QString MqttClientManager::handleQmtConnQuery() const
{
    qCDebug(mqttClientManagerLog) << "handleQmtConnQuery";
    
    QStringList results;
    
    for (int i = 0; i < MAX_CLIENTS; ++i) {
        MqttClient* client = getClient(i);
        if (client) {
            int state = client->getConnectionState();
            if (state > 1) { // 只显示有连接活动的客户端
                results << QString("+QMTCONN: %1,%2").arg(i).arg(state);
            }
        }
    }
    
    return results.join("\r\n");
}

// === 客户端事件处理槽实现 ===

void MqttClientManager::onClientNetworkOpened(int clientIdx, int result)
{
    qCDebug(mqttClientManagerLog) << "Client" << clientIdx << "network opened, result:" << result;
    emit networkOpened(clientIdx, result);
}

void MqttClientManager::onClientMqttConnected(int clientIdx, int result, int retCode)
{
    qCDebug(mqttClientManagerLog) << "Client" << clientIdx << "MQTT connected, result:" << result << "retCode:" << retCode;
    emit mqttConnected(clientIdx, result, retCode);
}

void MqttClientManager::onClientSubscribed(int clientIdx, int msgId, int result, int qos)
{
    qCDebug(mqttClientManagerLog) << "Client" << clientIdx << "subscribed, msgId:" << msgId << "result:" << result << "qos:" << qos;
    emit subscribed(clientIdx, msgId, result, qos);
}

void MqttClientManager::onClientPublished(int clientIdx, int msgId, int result)
{
    qCDebug(mqttClientManagerLog) << "Client" << clientIdx << "published, msgId:" << msgId << "result:" << result;
    emit published(clientIdx, msgId, result);
}

void MqttClientManager::onClientMqttDisconnected(int clientIdx, int result)
{
    qCDebug(mqttClientManagerLog) << "Client" << clientIdx << "MQTT disconnected, result:" << result;
    emit mqttDisconnected(clientIdx, result);
}

void MqttClientManager::onClientNetworkClosed(int clientIdx, int result)
{
    qCDebug(mqttClientManagerLog) << "Client" << clientIdx << "network closed, result:" << result;
    emit networkClosed(clientIdx, result);
}

void MqttClientManager::onClientMessageReceived(int clientIdx, int msgId, const QString& topic, 
                                                const QByteArray& payload)
{
    qCDebug(mqttClientManagerLog) << "Client" << clientIdx << "message received, topic:" << topic;
    emit messageReceived(clientIdx, msgId, topic, payload);
}

void MqttClientManager::onClientStatusChanged(int clientIdx, int errorCode)
{
    qCDebug(mqttClientManagerLog) << "Client" << clientIdx << "status changed, errorCode:" << errorCode;
    emit statusChanged(clientIdx, errorCode);
}

// === 内部辅助函数实现 ===

void MqttClientManager::initializeClients()
{
    qCDebug(mqttClientManagerLog) << "Initializing" << MAX_CLIENTS << "MQTT clients";
    
    for (int i = 0; i < MAX_CLIENTS; ++i) {
        MqttClient* client = new MqttClient(i, this);
        m_clients.insert(i, client);
        connectClientSignals(client);
        
        qCDebug(mqttClientManagerLog) << "Client" << i << "initialized";
    }
}

MqttClient* MqttClientManager::getClient(int clientIdx) const
{
    if (!isValidClientIndex(clientIdx)) {
        return nullptr;
    }
    
    return m_clients.value(clientIdx, nullptr);
}

bool MqttClientManager::isValidClientIndex(int clientIdx) const
{
    return (clientIdx >= 0 && clientIdx < MAX_CLIENTS);
}

void MqttClientManager::connectClientSignals(MqttClient* client)
{
    if (!client) {
        return;
    }
    
    // 连接所有客户端信号到管理器的槽函数
    connect(client, &MqttClient::networkOpened,
            this, &MqttClientManager::onClientNetworkOpened);
    
    connect(client, &MqttClient::mqttConnected,
            this, &MqttClientManager::onClientMqttConnected);
    
    connect(client, &MqttClient::subscribed,
            this, &MqttClientManager::onClientSubscribed);
    
    connect(client, &MqttClient::published,
            this, &MqttClientManager::onClientPublished);
    
    connect(client, &MqttClient::mqttDisconnected,
            this, &MqttClientManager::onClientMqttDisconnected);
    
    connect(client, &MqttClient::networkClosed,
            this, &MqttClientManager::onClientNetworkClosed);
    
    connect(client, &MqttClient::messageReceived,
            this, &MqttClientManager::onClientMessageReceived);
    
    connect(client, &MqttClient::statusChanged,
            this, &MqttClientManager::onClientStatusChanged);
}
