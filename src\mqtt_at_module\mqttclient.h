#ifndef MQTTCLIENT_H
#define MQTTCLIENT_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QTimer>
#include <QLoggingCategory>
#include <QtMqtt/QMqttClient>
#include <QtMqtt/QMqttTopicName>

Q_DECLARE_LOGGING_CATEGORY(mqttClientLog)

/**
 * @brief 单个MQTT客户端实现，支持AT指令接口
 *
 * 该类管理单个MQTT客户端实例的生命周期，
 * 响应AT指令并上报URC事件。
 * 兼容EC20等模块的MQTT行为。
 */
class MqttClient : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Client connection states
     */
    enum State {
        IDLE,           ///< Initial state
        NET_READY,      ///< AT+QMTOPEN completed, network configured
        CONNECTING,     ///< AT+QMTCONN in progress
        CONNECTED,      ///< MQTT connection established
        DISCONNECTING   ///< Disconnection in progress
    };

    /**
     * @brief Constructor
     * @param clientIdx Client index (0-5)
     * @param parent Parent object
     */
    explicit MqttClient(int clientIdx, QObject *parent = nullptr);
    
    /**
     * @brief Destructor
     */
    ~MqttClient();

    // === AT Command Processing Interface ===
    
    /**
     * @brief Open network connection (AT+QMTOPEN)
     * @param hostname MQTT server hostname or IP
     * @param port MQTT server port
     * @return true if command accepted, false if error
     */
    bool openNetwork(const QString& hostname, int port);
    
    /**
     * @brief Connect to MQTT broker (AT+QMTCONN)
     * @param clientId MQTT client identifier
     * @param username Optional username for authentication
     * @param password Optional password for authentication
     * @return true if command accepted, false if error
     */
    bool connectMqtt(const QString& clientId, 
                     const QString& username = QString(), 
                     const QString& password = QString());
    
    /**
     * @brief Subscribe to topic (AT+QMTSUB)
     * @param msgId Message ID provided by MCU
     * @param topic Topic to subscribe
     * @param qos Quality of Service level
     * @return true if command accepted, false if error
     */
    bool subscribe(int msgId, const QString& topic, int qos);
    
    /**
     * @brief Publish message (AT+QMTPUBEX)
     * @param msgId Message ID provided by MCU
     * @param qos Quality of Service level
     * @param retain Retain flag
     * @param topic Topic to publish
     * @param payload Message payload
     * @return true if command accepted, false if error
     */
    bool publish(int msgId, int qos, bool retain, 
                 const QString& topic, const QByteArray& payload);
    
    /**
     * @brief Disconnect from MQTT broker (AT+QMTDISC)
     * @return true if command accepted, false if error
     */
    bool disconnect();
    
    /**
     * @brief Close network connection (AT+QMTCLOSE)
     * @return true if command accepted, false if error
     */
    bool closeNetwork();

    // === Status Query Interface ===
    
    /**
     * @brief Check if network is open (AT+QMTOPEN?)
     * @return true if network is configured
     */
    bool isNetworkOpen() const;
    
    /**
     * @brief Get MQTT connection state (AT+QMTCONN?)
     * @return Connection state (1=init, 2=connecting, 3=connected, 4=disconnecting)
     */
    int getConnectionState() const;
    
    /**
     * @brief Get network information for query response
     * @return Formatted network info string
     */
    QString getNetworkInfo() const;

signals:
    // === URC Event Signals ===
    
    /**
     * @brief Network opened result (+QMTOPEN URC)
     * @param clientIdx Client index
     * @param result Result code (0=success)
     */
    void networkOpened(int clientIdx, int result);
    
    /**
     * @brief MQTT connection result (+QMTCONN URC)
     * @param clientIdx Client index
     * @param result Result code (0=success)
     * @param retCode MQTT return code
     */
    void mqttConnected(int clientIdx, int result, int retCode);
    
    /**
     * @brief Subscription result (+QMTSUB URC)
     * @param clientIdx Client index
     * @param msgId Message ID from AT command
     * @param result Result code (0=success)
     * @param qos Granted QoS level
     */
    void subscribed(int clientIdx, int msgId, int result, int qos);
    
    /**
     * @brief Publish result (+QMTPUBEX URC)
     * @param clientIdx Client index
     * @param msgId Message ID from AT command
     * @param result Result code (0=success)
     */
    void published(int clientIdx, int msgId, int result);
    
    /**
     * @brief MQTT disconnection result (+QMTDISC URC)
     * @param clientIdx Client index
     * @param result Result code (0=success)
     */
    void mqttDisconnected(int clientIdx, int result);
    
    /**
     * @brief Network closed result (+QMTCLOSE URC)
     * @param clientIdx Client index
     * @param result Result code (0=success)
     */
    void networkClosed(int clientIdx, int result);
    
    /**
     * @brief Message received (+QMTRECV URC)
     * @param clientIdx Client index
     * @param msgId Message ID
     * @param topic Topic name
     * @param payload Message payload
     */
    void messageReceived(int clientIdx, int msgId, const QString& topic, 
                        const QByteArray& payload);
    
    /**
     * @brief Status change notification (+QMTSTAT URC)
     * @param clientIdx Client index
     * @param errorCode Error code indicating status change reason
     */
    void statusChanged(int clientIdx, int errorCode);

private slots:
    // === QMqttClient Event Handlers ===
    void onMqttConnected();
    void onMqttDisconnected();
    void onMqttError(QMqttClient::ClientError error);
    void onMessageReceived(const QByteArray& message, const QMqttTopicName& topic);

private:
    // === Internal Helper Functions ===
    void setState(State newState);
    void emitNetworkOpenedAsync(int result);
    void emitMqttConnectedAsync(int result, int retCode);

    // === Member Variables ===
    
    // Basic identification
    int m_clientIdx;                    ///< Client index (0-5)
    
    // Connection information
    QString m_hostname;                 ///< MQTT server hostname
    int m_port;                        ///< MQTT server port
    QString m_clientId;                ///< MQTT client identifier
    QString m_username;                ///< Authentication username
    QString m_password;                ///< Authentication password
    
    // State management
    State m_state;                     ///< Current client state
    
    // MQTT instance
    QMqttClient* m_mqttClient;         ///< Actual MQTT client instance
    
    // Subscription management
    QMap<QString, int> m_subscriptions; ///< topic -> qos mapping
    
};

#endif // MQTTCLIENT_H
