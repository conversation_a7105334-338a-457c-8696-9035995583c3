#include "mqttclient.h"
#include <QDebug>
#include <QTimer>

Q_LOGGING_CATEGORY(mqttClientLog, "mqtt.client")

MqttClient::MqttClient(int clientIdx, QObject *parent)
    : QObject(parent)
    , m_clientIdx(clientIdx)
    , m_port(0)
    , m_state(IDLE)
    , m_mqttClient(nullptr)
    , m_urcTimer(new QTimer(this))
{
    qCDebug(mqttClientLog) << "Creating MQTT client with index:" << m_clientIdx;

    // Initialize MQTT client
    m_mqttClient = new QMqttClient(this);

    // Connect MQTT client signals
    connect(m_mqttClient, &QMqttClient::connected,
            this, &MqttClient::onMqttConnected);
    connect(m_mqttClient, &QMqttClient::disconnected,
            this, &MqttClient::onMqttDisconnected);
    connect(m_mqttClient, &QMqttClient::errorChanged,
            this, &MqttClient::onMqttError);
    connect(m_mqttClient, &QMqttClient::messageReceived,
            this, &MqttClient::onMessageReceived);

    // Configure URC timer
    m_urcTimer->setSingleShot(true);

    qCDebug(mqttClientLog) << "MQTT client" << m_clientIdx << "initialized successfully";
}

MqttClient::~MqttClient()
{
    qCDebug(mqttClientLog) << "Destroying MQTT client" << m_clientIdx;

    if (m_mqttClient && m_mqttClient->state() == QMqttClient::Connected) {
        qCDebug(mqttClientLog) << "Disconnecting MQTT client" << m_clientIdx << "during destruction";
        m_mqttClient->disconnectFromHost();
    }
}

bool MqttClient::openNetwork(const QString& hostname, int port)
{
    qCDebug(mqttClientLog) << "AT+QMTOPEN: Client" << m_clientIdx
                          << "opening network to" << hostname << ":" << port;

    // Validate parameters
    if (hostname.isEmpty() || port <= 0 || port > 65535) {
        qCWarning(mqttClientLog) << "Invalid parameters for openNetwork:" << hostname << port;
        return false;
    }

    // Check current state
    if (m_state != IDLE && m_state != NET_READY) {
        qCWarning(mqttClientLog) << "Cannot open network in current state:" << m_state;
        return false;
    }

    // Store connection parameters
    m_hostname = hostname;
    m_port = port;

    // Update state
    setState(NET_READY);

    // Emit async URC after short delay to simulate real module behavior
    emitNetworkOpenedAsync(0); // 0 = success

    qCDebug(mqttClientLog) << "Network configuration saved for client" << m_clientIdx;
    return true;
}

bool MqttClient::connectMqtt(const QString& clientId, const QString& username, const QString& password)
{
    qCDebug(mqttClientLog) << "AT+QMTCONN: Client" << m_clientIdx
                          << "connecting with clientId:" << clientId;

    // Validate parameters
    if (clientId.isEmpty()) {
        qCWarning(mqttClientLog) << "Client ID cannot be empty";
        return false;
    }

    // Check current state
    if (m_state != NET_READY) {
        qCWarning(mqttClientLog) << "Cannot connect MQTT in current state:" << m_state
                                << "- network must be opened first";
        return false;
    }

    // Store MQTT parameters
    m_clientId = clientId;
    m_username = username;
    m_password = password;

    // Update state
    setState(CONNECTING);

    // Configure MQTT client
    m_mqttClient->setHostname(m_hostname);
    m_mqttClient->setPort(static_cast<quint16>(m_port));
    m_mqttClient->setClientId(m_clientId);

    if (!m_username.isEmpty()) {
        m_mqttClient->setUsername(m_username);
    }
    if (!m_password.isEmpty()) {
        m_mqttClient->setPassword(m_password);
    }

    // Initiate connection (this combines TCP + MQTT connection)
    qCDebug(mqttClientLog) << "Initiating MQTT connection for client" << m_clientIdx;
    m_mqttClient->connectToHost();

    return true;
}

bool MqttClient::subscribe(int msgId, const QString& topic, int qos)
{
    qCDebug(mqttClientLog) << "AT+QMTSUB: Client" << m_clientIdx
                          << "subscribing to topic:" << topic
                          << "QoS:" << qos << "msgId:" << msgId;

    // Validate parameters
    if (topic.isEmpty() || qos < 0 || qos > 2 || msgId < 1 || msgId > 65535) {
        qCWarning(mqttClientLog) << "Invalid subscription parameters";
        return false;
    }

    // Check current state
    if (m_state != CONNECTED) {
        qCWarning(mqttClientLog) << "Cannot subscribe in current state:" << m_state;
        return false;
    }

    // Perform subscription
    auto subscription = m_mqttClient->subscribe(topic, static_cast<quint8>(qos));
    if (!subscription) {
        qCWarning(mqttClientLog) << "Failed to create subscription for topic:" << topic;
        emit subscribed(m_clientIdx, msgId, 2, 0); // 2 = failure
        return true; // Command was accepted, but operation failed
    }

    // Store subscription info
    m_subscriptions[topic] = qos;

    // Connect to subscription state change
    connect(subscription, &QMqttSubscription::stateChanged,
            [this, msgId, topic, qos](QMqttSubscription::SubscriptionState state) {
        if (state == QMqttSubscription::Subscribed) {
            qCDebug(mqttClientLog) << "Successfully subscribed to topic:" << topic;
            emit subscribed(m_clientIdx, msgId, 0, qos); // 0 = success
        } else if (state == QMqttSubscription::Error) {
            qCWarning(mqttClientLog) << "Subscription failed for topic:" << topic;
            m_subscriptions.remove(topic);
            emit subscribed(m_clientIdx, msgId, 2, 0); // 2 = failure
        }
    });

    return true;
}

bool MqttClient::publish(int msgId, int qos, bool retain, const QString& topic, const QByteArray& payload)
{
    qCDebug(mqttClientLog) << "AT+QMTPUBEX: Client" << m_clientIdx
                          << "publishing to topic:" << topic
                          << "QoS:" << qos << "retain:" << retain
                          << "msgId:" << msgId << "payload size:" << payload.size();

    // Validate parameters
    if (topic.isEmpty() || qos < 0 || qos > 2 || msgId < 1 || msgId > 65535) {
        qCWarning(mqttClientLog) << "Invalid publish parameters";
        return false;
    }

    // Check current state
    if (m_state != CONNECTED) {
        qCWarning(mqttClientLog) << "Cannot publish in current state:" << m_state;
        return false;
    }

    // Perform publish
    qint32 publishId = m_mqttClient->publish(topic, payload, static_cast<quint8>(qos), retain);
    if (publishId == -1) {
        qCWarning(mqttClientLog) << "Failed to publish message to topic:" << topic;
        emit published(m_clientIdx, msgId, 2); // 2 = failure
        return true; // Command was accepted, but operation failed
    }

    qCDebug(mqttClientLog) << "Message published successfully, publishId:" << publishId;

    // For QoS 0, emit success immediately
    // For QoS 1/2, we should wait for PUBACK/PUBCOMP, but QMqttClient doesn't provide easy access
    // So we emit success immediately for simplicity
    QTimer::singleShot(10, [this, msgId]() {
        emit published(m_clientIdx, msgId, 0); // 0 = success
    });

    return true;
}

bool MqttClient::disconnect()
{
    qCDebug(mqttClientLog) << "AT+QMTDISC: Client" << m_clientIdx << "disconnecting";

    // Check current state
    if (m_state != CONNECTED) {
        qCWarning(mqttClientLog) << "Cannot disconnect in current state:" << m_state;
        return false;
    }

    // Update state
    setState(DISCONNECTING);

    // Disconnect from MQTT broker
    m_mqttClient->disconnectFromHost();

    qCDebug(mqttClientLog) << "Disconnect initiated for client" << m_clientIdx;
    return true;
}

bool MqttClient::closeNetwork()
{
    qCDebug(mqttClientLog) << "AT+QMTCLOSE: Client" << m_clientIdx << "closing network";

    // Check current state - can close from any state except IDLE
    if (m_state == IDLE) {
        qCWarning(mqttClientLog) << "Network is not open for client" << m_clientIdx;
        return false;
    }

    // If still connected, disconnect first
    if (m_state == CONNECTED) {
        m_mqttClient->disconnectFromHost();
    }

    // Clear connection information
    m_hostname.clear();
    m_port = 0;
    m_clientId.clear();
    m_username.clear();
    m_password.clear();
    m_subscriptions.clear();

    // Update state
    setState(IDLE);

    // Emit async URC
    QTimer::singleShot(50, [this]() {
        emit networkClosed(m_clientIdx, 0); // 0 = success
    });

    qCDebug(mqttClientLog) << "Network closed for client" << m_clientIdx;
    return true;
}

// === Status Query Interface ===

bool MqttClient::isNetworkOpen() const
{
    return m_state >= NET_READY;
}

int MqttClient::getConnectionState() const
{
    switch (m_state) {
        case IDLE:
            return 1; // MQTT initialized
        case NET_READY:
            return 1; // MQTT initialized
        case CONNECTING:
            return 2; // MQTT connecting
        case CONNECTED:
            return 3; // MQTT connected
        case DISCONNECTING:
            return 4; // MQTT disconnecting
        default:
            return 1;
    }
}

QString MqttClient::getNetworkInfo() const
{
    if (m_state >= NET_READY) {
        return QString("+QMTOPEN: %1,\"%2\",%3")
               .arg(m_clientIdx)
               .arg(m_hostname)
               .arg(m_port);
    }
    return QString();
}

// === QMqttClient Event Handlers ===

void MqttClient::onMqttConnected()
{
    qCDebug(mqttClientLog) << "MQTT client" << m_clientIdx << "connected successfully";

    // Update state
    setState(CONNECTED);

    // Emit connection success URC
    emitMqttConnectedAsync(0, 0); // result=0 (success), retCode=0 (accepted)
}

void MqttClient::onMqttDisconnected()
{
    qCDebug(mqttClientLog) << "MQTT client" << m_clientIdx << "disconnected";

    if (m_state == DISCONNECTING) {
        // Normal disconnection
        setState(NET_READY);
        emit mqttDisconnected(m_clientIdx, 0); // 0 = success
    } else {
        // Unexpected disconnection
        qCWarning(mqttClientLog) << "Unexpected disconnection for client" << m_clientIdx;
        setState(NET_READY);
        emit statusChanged(m_clientIdx, 1); // 1 = connection reset by server
    }
}

void MqttClient::onMqttError(QMqttClient::ClientError error)
{
    qCWarning(mqttClientLog) << "MQTT client" << m_clientIdx << "error:" << error;

    if (m_state == CONNECTING) {
        // Connection failed
        setState(NET_READY);

        int retCode = 0;
        switch (error) {
            case QMqttClient::BadUsernameOrPassword:
                retCode = 4; // Bad username or password
                break;
            case QMqttClient::NotAuthorized:
                retCode = 5; // Not authorized
                break;
            default:
                retCode = 1; // Unacceptable protocol version or other error
                break;
        }

        emitMqttConnectedAsync(1, retCode); // result=1 (failure)
    } else {
        // Runtime error
        int errorCode = 1; // Default to connection reset
        switch (error) {
            case QMqttClient::TransportInvalid:
            case QMqttClient::ProtocolViolation:
                errorCode = 6; // Client actively disconnected due to send failure
                break;
            default:
                errorCode = 1; // Connection reset by server
                break;
        }

        emit statusChanged(m_clientIdx, errorCode);
    }
}

void MqttClient::onMessageReceived(const QByteArray& message, const QMqttTopicName& topic)
{
    qCDebug(mqttClientLog) << "MQTT client" << m_clientIdx
                          << "received message on topic:" << topic.name()
                          << "payload size:" << message.size();

    // Generate a message ID for the received message
    // In real implementation, this would come from the MQTT packet
    static int receivedMsgId = 1;

    emit messageReceived(m_clientIdx, receivedMsgId++, topic.name(), message);
}

// === Internal Helper Functions ===

void MqttClient::setState(State newState)
{
    if (m_state != newState) {
        qCDebug(mqttClientLog) << "Client" << m_clientIdx << "state changed from"
                              << m_state << "to" << newState;
        m_state = newState;
    }
}

void MqttClient::emitNetworkOpenedAsync(int result)
{
    // Emit URC after short delay to simulate real module behavior
    QTimer::singleShot(50, [this, result]() {
        qCDebug(mqttClientLog) << "Emitting +QMTOPEN URC for client" << m_clientIdx
                              << "result:" << result;
        emit networkOpened(m_clientIdx, result);
    });
}

void MqttClient::emitMqttConnectedAsync(int result, int retCode)
{
    // Emit URC after short delay to simulate real module behavior
    QTimer::singleShot(100, [this, result, retCode]() {
        qCDebug(mqttClientLog) << "Emitting +QMTCONN URC for client" << m_clientIdx
                              << "result:" << result << "retCode:" << retCode;
        emit mqttConnected(m_clientIdx, result, retCode);
    });
}
